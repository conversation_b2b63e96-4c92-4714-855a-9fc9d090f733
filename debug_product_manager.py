#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')
table3 = tables[2]  # table[3]

all_rows = table3.xpath('.//tr')

# 找到"产品经理"行
product_manager_row = None
for row in all_rows:
    if '产品经理' in row.text_content():
        product_manager_row = row
        break

if product_manager_row is not None:
    print("找到产品经理行")
    tr_index = all_rows.index(product_manager_row) + 1
    print(f"位置: tr[{tr_index}]")
    
    # 显示该行的单元格
    cells = product_manager_row.xpath('.//td | .//th')
    print(f"该行有 {len(cells)} 个单元格:")
    for i, cell in enumerate(cells):
        cell_text = cell.text_content().strip()
        rowspan = cell.get('rowspan', '1')
        colspan = cell.get('colspan', '1')
        print(f"  单元格 {i+1}: '{cell_text}' rowspan={rowspan} colspan={colspan}")
    
    # 查看前面几行，寻找影响当前行的rowspan
    print(f"\n查看前面几行的rowspan影响:")
    current_index = all_rows.index(product_manager_row)
    
    for i in range(max(0, current_index - 5), current_index):
        prev_row = all_rows[i]
        prev_cells = prev_row.xpath('.//td | .//th')
        prev_tr_index = i + 1
        
        print(f"\n  检查 tr[{prev_tr_index}]:")
        col_offset = 0
        for j, cell in enumerate(prev_cells):
            cell_text = cell.text_content().strip()
            rowspan = int(cell.get('rowspan', 1))
            colspan = int(cell.get('colspan', 1))
            
            # 检查是否影响当前行
            if i + rowspan > current_index:
                print(f"    单元格{j+1}: '{cell_text[:20]}...' rowspan={rowspan} colspan={colspan} -> 影响列{col_offset}到{col_offset+colspan-1}")
            else:
                print(f"    单元格{j+1}: '{cell_text[:20]}...' rowspan={rowspan} colspan={colspan}")
            
            col_offset += colspan
    
    # 模拟正确的列映射
    print(f"\n正确的列映射应该是:")
    header_columns = ['项目', '类别', '描述', '必选/可选', '单位', '单价（元）', '数量', '总价（元）']
    
    # 计算合并单元格影响
    merged_values = {}
    for i in range(max(0, current_index - 5), current_index):
        prev_row = all_rows[i]
        prev_cells = prev_row.xpath('.//td | .//th')
        
        col_offset = 0
        for cell in prev_cells:
            rowspan = int(cell.get('rowspan', 1))
            colspan = int(cell.get('colspan', 1))
            
            # 如果rowspan影响到当前行
            if i + rowspan > current_index:
                cell_value = cell.text_content().strip()
                if cell_value:
                    # 为所有受影响的列设置值
                    for c in range(colspan):
                        merged_values[col_offset + c] = cell_value
                        print(f"  列{col_offset + c} ({header_columns[col_offset + c] if col_offset + c < len(header_columns) else '未知'}): {cell_value}")
            
            col_offset += colspan
    
    # 当前行的单元格映射
    print(f"\n当前行单元格映射:")
    current_cell_index = 0
    for col_index in range(len(header_columns)):
        if col_index in merged_values:
            print(f"  列{col_index} ({header_columns[col_index]}): {merged_values[col_index]} (来自合并单元格)")
        elif current_cell_index < len(cells):
            cell = cells[current_cell_index]
            cell_value = cell.text_content().strip()
            print(f"  列{col_index} ({header_columns[col_index]}): {cell_value} (当前行单元格{current_cell_index+1})")
            current_cell_index += 1
        else:
            print(f"  列{col_index} ({header_columns[col_index]}): (空)")
