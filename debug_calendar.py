#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')

# 查找包含"日历管理"的行
for table_idx, table in enumerate(tables):
    all_rows = table.xpath('.//tr')
    for row_idx, row in enumerate(all_rows):
        row_text = row.text_content()
        if '日历管理' in row_text:
            print(f"找到'日历管理'在table[{table_idx+1}]的第{row_idx+1}行")
            print(f"行内容: {row_text}")
            
            # 显示该行的所有单元格
            cells = row.xpath('.//td | .//th')
            print(f"该行有 {len(cells)} 个单元格:")
            for i, cell in enumerate(cells):
                cell_text = cell.text_content().strip()
                rowspan = cell.get('rowspan', '1')
                colspan = cell.get('colspan', '1')
                print(f"  单元格 {i+1}: '{cell_text}' rowspan={rowspan} colspan={colspan}")
            
            print("="*50)
