#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree
import sys
sys.path.append('src')

from controller.parser.chunker.base import Chunker
from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER

# 读取HTML文件
with open("1.html", "r", encoding="utf-8") as f:
    html_content = f.read()

# 解析HTML
tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
root = tree.getroottree()

# 获取第3个table
tables = tree.xpath('//table')
if len(tables) >= 3:
    table3 = tables[2]  # 第3个table (索引从0开始)
    
    print(f"第3个table的xpath: {root.getpath(table3)}")
    
    # 创建Chunker实例
    chunker = Chunker(tokenizer=BGE_M3_TOKENIZER)
    
    # 测试新的表格处理逻辑
    try:
        chunker.chunk_large_table_tag(table3, root)
        
        print(f"\n生成了 {len(chunker.chunks)} 个chunks:")
        for i, chunk in enumerate(chunker.chunks):
            print(f"\nChunk {i+1}:")
            print(f"  Nodes: {len(chunk.nodes)}")
            if chunk.nodes:
                print(f"  XPath: {chunk.nodes[0].xpath}")
                print(f"  Plain content: {chunk.nodes[0].plain_content[:100]}...")
                
    except Exception as e:
        print(f"处理表格时出错: {e}")
        import traceback
        traceback.print_exc()
