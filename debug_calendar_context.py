#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')

# 查看table[2]中"日历管理"行的上下文
table2 = tables[1]  # table[2] (索引从0开始)
all_rows = table2.xpath('.//tr')

print(f"Table[2]总共有 {len(all_rows)} 行")

# 找到"日历管理"行
calendar_row_indices = []
for i, row in enumerate(all_rows):
    if '日历管理' in row.text_content():
        calendar_row_indices.append(i)

print(f"找到'日历管理'在行: {[i+1 for i in calendar_row_indices]}")

# 查看第一个"日历管理"行的上下文
if calendar_row_indices:
    target_index = calendar_row_indices[0]  # 第51行
    print(f"\n查看第{target_index+1}行('日历管理')的上下文:")
    
    # 显示前后几行
    start = max(0, target_index - 3)
    end = min(len(all_rows), target_index + 4)
    
    for i in range(start, end):
        row = all_rows[i]
        cells = row.xpath('.//td | .//th')
        row_text = row.text_content().strip()
        
        marker = " >>> " if i == target_index else "     "
        print(f"{marker}行{i+1}: {len(cells)}列 - '{row_text[:50]}...'")
        
        if i == target_index:
            # 详细显示目标行
            for j, cell in enumerate(cells):
                cell_text = cell.text_content().strip()
                rowspan = cell.get('rowspan', '1')
                colspan = cell.get('colspan', '1')
                print(f"        单元格{j+1}: '{cell_text}' rowspan={rowspan} colspan={colspan}")

# 按列数分组测试
def get_effective_column_count(row):
    cells = row.xpath('.//td | .//th')
    if not cells:
        return 0
    
    total_cols = 0
    for cell in cells:
        colspan = int(cell.get('colspan', 1))
        total_cols += colspan
    return total_cols

# 统计各种列数的行
column_stats = {}
for row in all_rows:
    col_count = get_effective_column_count(row)
    if col_count > 0:
        if col_count not in column_stats:
            column_stats[col_count] = 0
        column_stats[col_count] += 1

print(f"\n列数统计:")
for col_count in sorted(column_stats.keys()):
    print(f"  {col_count}列: {column_stats[col_count]}行")
