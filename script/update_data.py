#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio

from sqlalchemy import update
from model.doc import DocModel

from engine.rdb import load_session_context, g
from controller.repository import Doc



@load_session_context
async def update_tags():
    _, docs = await Doc.get_list(page=1, per_page=20, order_by="create_time:desc")
    for doc in docs:
        try:
            source = await Doc.get_es_one(
                repo_id=doc["repo_id"],
                doc_id=doc["doc_id"],
                includes=["extract_result", "keywords", "data_time", "reference_type"])
            await Doc.update(
                doc_id=doc["doc_id"], data_time=source["data_time"], keywords=source["keywords"],
                sentiment_score=source["extract_result"].get("sentiment_score") if source.get("extract_result") else None,
                reference_type=source.get("reference_type", "html"))

        except Exception as e:
            print("失败", doc["doc_id"], str(e))
            query = (update(DocModel)
                 .where(DocModel.id == doc["doc_id"])
                 .values({DocModel.is_delete: 1}))
            await g.session.execute(query)
            await g.session.commit()
        else:
            print("成功", doc["doc_id"])
        await g.session.commit()


if __name__ == '__main__':
    asyncio.run(update_tags())