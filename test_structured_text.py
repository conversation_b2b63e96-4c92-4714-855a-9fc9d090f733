#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')
table3 = tables[2]
all_rows = table3.xpath('.//tr')

# 找到列头行
header_columns = ['项目', '类别', '描述', '必选/可选', '单位', '单价（元）', '数量', '总价（元）']

# 找到"UI/UE 设计"行
ui_ue_row = None
for row in all_rows:
    if 'UI/UE' in row.text_content() and '设计' in row.text_content():
        ui_ue_row = row
        break

if ui_ue_row is not None:
    print("找到UI/UE设计行")
    
    # 获取所有数据行（用于合并单元格处理）
    def is_data_row(row):
        cells = row.xpath('.//td | .//th')
        if len(cells) == 1 and cells[0].get('colspan'):
            return False
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text and cell_text not in ['', ' ', '\n']:
                return True
        return False
    
    data_rows = [row for row in all_rows if is_data_row(row)]
    
    # 测试结构化文本提取
    def extract_structured_text(header_columns, row, all_data_rows):
        cells = row.xpath('.//td | .//th')
        if not cells:
            return ""
        
        # 获取合并单元格值
        def get_merged_cell_values(current_row, all_data_rows):
            merged_values = {}
            try:
                current_index = all_data_rows.index(current_row)
            except ValueError:
                return merged_values
            
            # 检查前面的行是否有rowspan影响当前行
            for i in range(max(0, current_index - 10), current_index):
                prev_row = all_data_rows[i]
                prev_cells = prev_row.xpath('.//td | .//th')
                
                col_offset = 0
                for cell in prev_cells:
                    rowspan = int(cell.get('rowspan', 1))
                    colspan = int(cell.get('colspan', 1))
                    
                    # 如果rowspan影响到当前行
                    if i + rowspan > current_index:
                        cell_value = cell.text_content().strip()
                        if cell_value:
                            # 为所有受影响的列设置值
                            for c in range(colspan):
                                merged_values[col_offset + c] = cell_value
                    
                    col_offset += colspan
            
            return merged_values
        
        merged_cell_values = get_merged_cell_values(row, all_data_rows)
        
        # 构建结构化文本
        text_parts = []
        current_cell_index = 0
        
        for col_index, header in enumerate(header_columns):
            cell_value = ""
            
            # 检查是否有来自前面行的合并单元格
            if col_index in merged_cell_values:
                cell_value = merged_cell_values[col_index]
                print(f"列 {col_index} ({header}): 来自合并单元格 = '{cell_value}'")
            elif current_cell_index < len(cells):
                # 使用当前行的单元格
                cell = cells[current_cell_index]
                cell_value = cell.text_content().strip()
                print(f"列 {col_index} ({header}): 当前单元格 = '{cell_value}'")
                
                # 检查colspan
                colspan = int(cell.get('colspan', 1))
                current_cell_index += 1
                
                # 如果有colspan，为后续列也使用相同的值
                if colspan > 1:
                    for i in range(1, colspan):
                        if col_index + i < len(header_columns):
                            next_header = header_columns[col_index + i]
                            if cell_value:
                                text_parts.append(f"{next_header}={cell_value}")
            
            # 添加当前列的内容
            if cell_value:
                text_parts.append(f"{header}={cell_value}")
        
        return " ".join(text_parts)
    
    structured_text = extract_structured_text(header_columns, ui_ue_row, data_rows)
    print(f"\n结构化文本: {structured_text}")
    
    # 显示UI/UE行的详细信息
    print(f"\nUI/UE行详细信息:")
    cells = ui_ue_row.xpath('.//td | .//th')
    for i, cell in enumerate(cells):
        cell_text = cell.text_content().strip()
        rowspan = cell.get('rowspan', '1')
        colspan = cell.get('colspan', '1')
        print(f"  单元格 {i+1}: '{cell_text}' rowspan={rowspan} colspan={colspan}")
else:
    print("未找到UI/UE设计行")
