from engine.es import es_sync
from engine.rdb import load_session_context, g
from controller.repository import Doc

res = es_sync.search(index="repo_*", size=10000, query={"match_all": {}}, source_includes=["doc_id"])

@load_session_context
async def get_all_docs():
    _, docs = await Doc.get_list(page=1, per_page=10000)
    doc_ids = [doc["doc_id"] for doc in docs]
    for hit in res["hits"]["hits"]:
        if hit["_source"]["doc_id"] not in doc_ids:
            print(hit["_source"]["doc_id"])


if __name__ == '__main__':
    import asyncio
    asyncio.run(get_all_docs())