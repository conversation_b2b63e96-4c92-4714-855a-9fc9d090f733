#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree
import sys
sys.path.append('src')

# 模拟新的鲁棒实现
class RobustTableChunker:
    def __init__(self):
        self.remove_html_blank = lambda x: x
        self.nodes = []
    
    def _get_valid_table_rows(self, element):
        """获取表格中所有有效的行"""
        all_rows = element.xpath('.//tr')
        valid_rows = []
        
        for row in all_rows:
            cells = row.xpath('.//td | .//th')
            if not cells:
                continue
                
            has_content = False
            for cell in cells:
                cell_text = cell.text_content().strip()
                if cell_text and cell_text not in ['', ' ', '\n', '\t']:
                    has_content = True
                    break
            
            if has_content:
                valid_rows.append(row)
        
        return valid_rows

    def _calculate_row_column_count(self, row):
        """计算行的有效列数"""
        cells = row.xpath('.//td | .//th')
        total_cols = 0
        for cell in cells:
            colspan = int(cell.get('colspan', 1))
            total_cols += colspan
        return total_cols

    def _partition_table_into_blocks(self, all_rows):
        """将表格行按逻辑结构分块"""
        if not all_rows:
            return []
        
        blocks = []
        current_block_rows = []
        current_column_count = None
        
        for row in all_rows:
            row_column_count = self._calculate_row_column_count(row)
            
            if current_column_count is not None and row_column_count != current_column_count:
                if current_block_rows:
                    blocks.append(self._create_table_block(current_block_rows, current_column_count))
                current_block_rows = [row]
                current_column_count = row_column_count
            else:
                current_block_rows.append(row)
                current_column_count = row_column_count
        
        if current_block_rows:
            blocks.append(self._create_table_block(current_block_rows, current_column_count))
        
        return blocks

    def _create_table_block(self, rows, column_count):
        """创建表格块对象"""
        return {
            'rows': rows,
            'column_count': column_count,
            'header_row': None,
            'header_columns': [],
            'data_rows': []
        }

    def _is_likely_header_row(self, row, expected_columns):
        """判断是否可能是列头行"""
        cells = row.xpath('.//td | .//th')
        if not cells:
            return False
        
        # 检查1: 是否使用th标签
        th_count = len(row.xpath('.//th'))
        if th_count > 0 and th_count == len(cells):
            return True
        
        # 检查2: 是否有格式化标签
        formatted_cells = 0
        total_cells_with_content = 0
        
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text:
                total_cells_with_content += 1
                if (cell.xpath('.//b') or cell.xpath('.//strong') or 
                    cell.xpath('.//i') or cell.xpath('.//em')):
                    formatted_cells += 1
        
        if total_cells_with_content > 1 and formatted_cells >= total_cells_with_content * 0.7:
            return True
        
        # 检查3: 内容特征
        return self._has_header_like_content(cells)

    def _has_header_like_content(self, cells):
        """检查单元格内容是否像列头"""
        header_keywords = ['名称', '类别', '描述', '价格', '数量', '金额', '项目', '类型', 
                          '状态', '时间', '日期', '备注', '说明', '单位', '规格', '服务']
        
        header_like_count = 0
        total_cells = 0
        
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text and len(cell_text) <= 20:
                total_cells += 1
                if any(keyword in cell_text for keyword in header_keywords):
                    header_like_count += 1
                elif len(cell_text) <= 10 and not cell_text.isdigit():
                    header_like_count += 1
        
        return total_cells > 1 and header_like_count >= total_cells * 0.5

    def _is_separator_like_row(self, row):
        """判断是否是分隔行"""
        cells = row.xpath('.//td | .//th')
        if len(cells) == 1:
            cell = cells[0]
            if cell.get('colspan'):
                return True
            cell_text = cell.text_content().strip()
            if cell_text and ('报价' in cell_text or '小计' in cell_text or '合计' in cell_text or '目录' in cell_text):
                return True
        return False

    def _extract_header_texts(self, row):
        """提取行中的文本作为列头"""
        cells = row.xpath('.//td | .//th')
        headers = []
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text:
                headers.append(cell_text)
            else:
                headers.append(f"列{len(headers)+1}")
        return headers

    def _identify_block_header(self, block):
        """识别块的列头行"""
        rows = block['rows']
        if not rows:
            return
        
        for i, row in enumerate(rows[:3]):
            if self._is_likely_header_row(row, block['column_count']):
                block['header_row'] = row
                block['header_columns'] = self._extract_header_texts(row)
                return
        
        if rows and block['column_count'] > 1:
            first_row = rows[0]
            if not self._is_separator_like_row(first_row):
                block['header_columns'] = [f"列{i+1}" for i in range(block['column_count'])]

    def _separate_data_rows(self, block):
        """分离数据行"""
        data_rows = []
        for row in block['rows']:
            if (row != block['header_row'] and 
                not self._is_separator_like_row(row)):
                data_rows.append(row)
        block['data_rows'] = data_rows

    def _extract_cell_text(self, cell):
        """提取单元格的纯文本内容"""
        if cell is None:
            return ""
        text = cell.text_content().strip()
        text = ' '.join(text.split())
        return text

    def _extract_structured_text_robust(self, header_columns, row, all_data_rows):
        """鲁棒的结构化文本提取"""
        if not row:
            return ""
        
        cells = row.xpath('.//td | .//th')
        if not cells:
            return ""
        
        text_parts = []
        
        if not header_columns:
            for i, cell in enumerate(cells):
                cell_value = self._extract_cell_text(cell)
                if cell_value:
                    text_parts.append(f"内容{i+1}={cell_value}")
            return " ".join(text_parts)
        
        cell_index = 0
        for col_index, header in enumerate(header_columns):
            cell_value = ""
            
            if cell_index < len(cells):
                cell = cells[cell_index]
                cell_value = self._extract_cell_text(cell)
                
                colspan = int(cell.get('colspan', 1))
                cell_index += 1
                
                if colspan > 1:
                    for i in range(1, colspan):
                        if col_index + i < len(header_columns):
                            next_header = header_columns[col_index + i]
                            if cell_value:
                                text_parts.append(f"{next_header}={cell_value}")
            
            if cell_value:
                text_parts.append(f"{header}={cell_value}")
        
        return " ".join(text_parts)

    def test_table(self, html_file):
        """测试表格处理"""
        print(f"\n=== 测试 {html_file} ===")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
        tables = tree.xpath('//table')
        
        for table_idx, table in enumerate(tables):
            print(f"\n表格 {table_idx + 1}:")
            
            # 获取有效行
            valid_rows = self._get_valid_table_rows(table)
            print(f"  有效行数: {len(valid_rows)}")
            
            # 分块
            blocks = self._partition_table_into_blocks(valid_rows)
            print(f"  分块数: {len(blocks)}")
            
            for block_idx, block in enumerate(blocks):
                print(f"\n  块 {block_idx + 1}: {block['column_count']}列, {len(block['rows'])}行")
                
                # 识别列头
                self._identify_block_header(block)
                if block['header_row'] is not None:
                    print(f"    列头: {block['header_columns']}")
                else:
                    print(f"    无明显列头, 使用默认: {block['header_columns']}")
                
                # 分离数据行
                self._separate_data_rows(block)
                print(f"    数据行: {len(block['data_rows'])}行")
                
                # 处理前几个数据行
                for row_idx, row in enumerate(block['data_rows'][:3]):
                    structured_text = self._extract_structured_text_robust(
                        block['header_columns'], row, block['data_rows'])
                    print(f"      行{row_idx + 1}: {structured_text}")

# 测试所有表格
chunker = RobustTableChunker()
test_files = ['test_table1.html', 'test_table2.html', 'test_table3.html', '1.html']

for test_file in test_files:
    try:
        chunker.test_table(test_file)
    except Exception as e:
        print(f"\n=== 测试 {test_file} 失败 ===")
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
