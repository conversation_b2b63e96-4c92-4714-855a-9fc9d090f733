#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

# 模拟Chunker类的核心方法
class MockChunker:
    def __init__(self):
        self.remove_html_blank = lambda x: x
        self.nodes = []
    
    def _detect_table_headers(self, all_rows):
        """智能识别表格列头"""
        if not all_rows:
            return None, []
        
        # 检查前几行，寻找列头
        for i, row in enumerate(all_rows[:15]):
            # 跳过分隔行
            if self._is_separator_row(row):
                continue
                
            # 检查是否为列头行：所有单元格都加粗，且不是分隔行
            cells = row.xpath('.//td | .//th')
            if not cells:
                continue
            
            # 排除分隔行（只有一个单元格且有colspan）
            if len(cells) == 1 and cells[0].get('colspan'):
                continue
                
            # 检查是否所有单元格都包含加粗标签
            bold_count = 0
            total_cells = 0
            header_columns = []
            
            for cell in cells:
                cell_text = cell.text_content().strip()
                if cell_text:  # 只计算有内容的单元格
                    total_cells += 1
                    # 检查是否包含加粗标签
                    if cell.xpath('.//b') or cell.xpath('.//strong'):
                        bold_count += 1
                        header_columns.append(cell_text)
                    else:
                        header_columns.append(cell_text)
            
            # 如果大部分单元格都加粗，且有多列，认为是列头行
            if total_cells > 3 and bold_count >= total_cells * 0.8:
                return row, header_columns
        
        return None, []

    def _is_separator_row(self, row):
        """判断是否为分隔行"""
        cells = row.xpath('.//td | .//th')
        if len(cells) == 1:
            cell = cells[0]
            if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
                return True
        return False

    def _is_data_row(self, row):
        """判断是否为数据行"""
        if self._is_separator_row(row):
            return False
        
        cells = row.xpath('.//td | .//th')
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text and cell_text not in ['', ' ', '\n']:
                return True
        return False

    def _build_row_table_html(self, header_row, data_row):
        """构建包含列头和数据行的table HTML"""
        table_parts = []
        
        if header_row is not None:
            header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
            table_parts.append(header_html)
        
        row_html = lxmlhtml.tostring(data_row, encoding="unicode", pretty_print=False)
        table_parts.append(row_html)
        
        tbody_content = "".join(table_parts)
        return f"<table><tbody>{tbody_content}</tbody></table>"

    def _extract_row_structured_text(self, header_columns, row, all_data_rows):
        """提取行的结构化纯文本，格式：列头=内容"""
        if not row:
            return ""
        
        cells = row.xpath('.//td | .//th')
        if not cells:
            return ""
        
        # 处理合并单元格
        merged_cell_values = self._get_merged_cell_values(row, all_data_rows)
        
        # 构建结构化文本
        text_parts = []
        current_cell_index = 0
        
        for col_index, header in enumerate(header_columns):
            cell_value = ""
            
            # 检查是否有来自前面行的合并单元格
            if col_index in merged_cell_values:
                cell_value = merged_cell_values[col_index]
            elif current_cell_index < len(cells):
                # 使用当前行的单元格
                cell = cells[current_cell_index]
                cell_value = cell.text_content().strip()
                
                # 检查colspan
                colspan = int(cell.get('colspan', 1))
                current_cell_index += 1
                
                # 如果有colspan，为后续列也使用相同的值
                if colspan > 1:
                    for i in range(1, colspan):
                        if col_index + i < len(header_columns):
                            next_header = header_columns[col_index + i]
                            if cell_value:
                                text_parts.append(f"{next_header}={cell_value}")
            
            # 添加当前列的内容
            if cell_value:
                text_parts.append(f"{header}={cell_value}")
        
        return " ".join(text_parts)

    def _get_merged_cell_values(self, current_row, all_data_rows):
        """获取影响当前行的合并单元格值"""
        merged_values = {}
        
        try:
            current_index = all_data_rows.index(current_row)
        except ValueError:
            return merged_values
        
        # 检查前面的行是否有rowspan影响当前行
        for i in range(max(0, current_index - 10), current_index):
            prev_row = all_data_rows[i]
            prev_cells = prev_row.xpath('.//td | .//th')
            
            col_offset = 0
            for cell in prev_cells:
                rowspan = int(cell.get('rowspan', 1))
                colspan = int(cell.get('colspan', 1))
                
                # 如果rowspan影响到当前行
                if i + rowspan > current_index:
                    cell_value = cell.text_content().strip()
                    if cell_value:
                        # 为所有受影响的列设置值
                        for c in range(colspan):
                            merged_values[col_offset + c] = cell_value
                
                col_offset += colspan
        
        return merged_values

# 测试完整逻辑
with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
root = tree.getroottree()
tables = tree.xpath('//table')
table3 = tables[2]

chunker = MockChunker()

# 获取所有行
all_rows = table3.xpath('.//tr')
print(f"表格总共有 {len(all_rows)} 行")

# 智能识别列头
header_row, header_columns = chunker._detect_table_headers(all_rows)
print(f"识别到列头: {header_columns}")

# 获取数据行
data_rows = [row for row in all_rows if row != header_row and chunker._is_data_row(row)]
print(f"找到 {len(data_rows)} 个数据行")

# 测试几个关键行
test_rows = []
for row in data_rows:
    row_text = row.text_content()
    if any(keyword in row_text for keyword in ['UI/UE', '产品经理', '前端开发']):
        test_rows.append(row)

print(f"\n测试 {len(test_rows)} 个关键行:")
for i, row in enumerate(test_rows):
    tr_index = all_rows.index(row) + 1
    structured_text = chunker._extract_row_structured_text(header_columns, row, data_rows)
    print(f"\n行 {i+1} (tr[{tr_index}]):")
    print(f"  结构化文本: {structured_text}")
