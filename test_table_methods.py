#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

# 读取HTML文件
with open("1.html", "r", encoding="utf-8") as f:
    html_content = f.read()

# 解析HTML
tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
root = tree.getroottree()

# 获取第3个table
tables = tree.xpath('//table')
if len(tables) >= 3:
    table3 = tables[2]  # 第3个table (索引从0开始)
    
    print(f"第3个table的xpath: {root.getpath(table3)}")
    
    # 测试智能识别列头的逻辑
    all_rows = table3.xpath('.//tr')
    print(f"总共有 {len(all_rows)} 行")
    
    # 手动实现检测列头的逻辑
    def detect_headers(all_rows):
        for i, row in enumerate(all_rows[:15]):  # 检查前15行
            cells = row.xpath('.//td | .//th')
            if not cells:
                continue

            # 排除分隔行（只有一个单元格且有colspan）
            if len(cells) == 1 and cells[0].get('colspan'):
                print(f"\n跳过第 {i+1} 行: 分隔行")
                continue

            print(f"\n检查第 {i+1} 行:")
            bold_count = 0
            total_cells = 0
            header_columns = []

            for j, cell in enumerate(cells):
                cell_text = cell.text_content().strip()
                if cell_text:
                    total_cells += 1
                    has_bold = bool(cell.xpath('.//b') or cell.xpath('.//strong'))
                    print(f"  单元格 {j+1}: '{cell_text[:20]}...' 加粗: {has_bold}")
                    if has_bold:
                        bold_count += 1
                    header_columns.append(cell_text)

            print(f"  加粗单元格: {bold_count}/{total_cells}")
            if total_cells > 3 and bold_count >= total_cells * 0.8:
                print(f"  -> 识别为列头行!")
                return row, header_columns
        
        return None, []
    
    header_row, header_columns = detect_headers(all_rows)
    
    if header_row is not None:
        print(f"\n找到列头行，包含 {len(header_columns)} 列:")
        for i, col in enumerate(header_columns):
            print(f"  列 {i+1}: {col}")
    else:
        print("\n未找到明显的列头行")
    
    # 测试数据行识别
    def is_data_row(row):
        cells = row.xpath('.//td | .//th')
        if len(cells) == 1:
            cell = cells[0]
            if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
                return False
        
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text and cell_text not in ['', ' ', '\n']:
                return True
        return False
    
    data_rows = [row for row in all_rows if row != header_row and is_data_row(row)]
    print(f"\n找到 {len(data_rows)} 个数据行")
    
    # 测试前几个数据行
    for i, row in enumerate(data_rows[:3]):
        tr_index = all_rows.index(row) + 1
        print(f"\n数据行 {i+1} (tr[{tr_index}]):")
        cells = row.xpath('.//td | .//th')
        for j, cell in enumerate(cells):
            cell_text = cell.text_content().strip()
            rowspan = cell.get('rowspan', '1')
            colspan = cell.get('colspan', '1')
            print(f"  单元格 {j+1}: '{cell_text[:30]}...' rowspan={rowspan} colspan={colspan}")
