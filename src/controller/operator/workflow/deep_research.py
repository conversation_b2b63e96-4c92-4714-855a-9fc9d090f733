import asyncio
import json
import logging
from datetime import datetime
from enum import StrEnum
from typing import AsyncIterable, Sequence, Optional, Annotated, List, Any

from autogen_agentchat.base import Response
from autogen_agentchat.messages import TextMessage, BaseChatMessage, ToolCallExecutionEvent
from autogen_core import CancellationToken
from json_repair import json_repair
from pydantic import BaseModel, Field

from common.logger import logger
from controller.engine import deepseek_v3_0324, deepseek_r1_0528, deepseek_v3_1_0821, deepseek_v3_1_0821_thinking
from controller.operator.agent.deep_research.coordinator import CoordinatorAgent
from controller.operator.agent.deep_research.planner import PlannerAgent, StructurePlanAgent, ResearchPlan
from controller.operator.agent.deep_research.researcher import research_teams, ResearchStage
from controller.operator.agent.deep_research.summarizer import SummaryAgent, ReflectAgent, IterateAgent, SummaryResponse
from controller.operator.agent.deep_research.reporter import SubReporterAgent, ReporterAgent
from controller.operator.chunking import RetrieveChunkModel
from controller.operator.workflow import BaseWorkflow


class DeepResearchStage(StrEnum):
    ANSWER = "answer"
    PLANNING = "planning"
    THINKING = "thinking"
    REPORTING = "reporting"
    START_REPORTING = "start_reporting"
    RESEARCHING = "researching"
    ERROR = "error"
    KEEP_ALIVE = "keep_alive"
    STOP = "stop"


class DeepResearchResponse(BaseModel):
    stage: Annotated[DeepResearchStage, Field(title="思考阶段")]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    title: Annotated[Optional[str], Field(title="标题")] = None
    content: Annotated[Optional[str], Field(title="回复内容")] = None
    reference: Annotated[Optional[Any], Field(title="参考文献列表")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class DeepResearchPlanWorkflow(BaseWorkflow):
    def __init__(self, user_prompt: str, use_web_search: bool = True):
        super().__init__()

        self.user_prompt = user_prompt
        self.use_web_search = use_web_search

    async def _run(self) -> AsyncIterable:
        coordinator_agent = CoordinatorAgent(model_client=deepseek_v3_0324)
        messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.user_prompt)]
        cancellation_token = CancellationToken()
        use_tool, response = False, None
        async for message in coordinator_agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, ToolCallExecutionEvent):
                use_tool = True
            elif isinstance(message, Response):
                response = message

        if not use_tool:
            logger.info("No tool used, returning response directly.")
            yield DeepResearchResponse(stage=DeepResearchStage.ANSWER, content=response.chat_message.content)

        else:
            planner_agent = PlannerAgent(model_client=deepseek_v3_0324)
            messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.user_prompt)]
            cancellation_token = CancellationToken()

            async for message in planner_agent.on_messages_stream(messages, cancellation_token):
                if isinstance(message, Response):
                    response = message

            plan = self.get_markdown_content(response.chat_message.content)
            logger.info(plan)
            yield DeepResearchResponse(stage=DeepResearchStage.PLANNING, content=plan)


class DeepResearchSearchWorkflow(BaseWorkflow):
    def __init__(self, user_prompt: str, plan: str, use_web_search: bool = True):
        super().__init__()

        self.user_prompt = user_prompt
        self.plan = plan
        self.use_web_search = use_web_search
        self.stop_flag = False

    async def _run(self) -> AsyncIterable:
        structure_plan_agent = StructurePlanAgent(model_client=deepseek_v3_0324)
        messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.plan)]
        cancellation_token = CancellationToken()

        response = await structure_plan_agent.on_messages(messages, cancellation_token)
        plan_json = self.get_json_content(response.chat_message.content)
        plan = ResearchPlan.model_validate(json.loads(plan_json))
        last_report = ""

        report_list = []
        research_results = []
        deduplication_results = set()

        for sub_plan in plan.plan:
            if self.stop_flag:
                break
            logger.info(f"Processing sub-plan: {sub_plan.description}")

            if last_report:
                content = f"上次报告内容：{last_report}\n\n当前计划：{sub_plan.description}"
            else:
                content = f"当前计划：{sub_plan.description}"

            summary_agent = SummaryAgent()
            messages: List[BaseChatMessage] = [
                TextMessage(source="user", content=content),
            ]

            response = await summary_agent.on_messages(messages, cancellation_token)
            res = response.chat_message.content
            res_json = self.get_json_content(res)
            res_struct = SummaryResponse.model_validate(json_repair.loads(res_json))
            yield DeepResearchResponse(stage=DeepResearchStage.THINKING, content=res_struct.content,
                                       title=res_struct.title)

            reflect_agent = ReflectAgent()
            messages.append(TextMessage(source="assistant", content=response.chat_message.content))

            response = await reflect_agent.on_messages(messages, cancellation_token)
            res = response.chat_message.content
            res_json = self.get_json_content(res)
            res_struct = SummaryResponse.model_validate(json_repair.loads(res_json))
            yield DeepResearchResponse(stage=DeepResearchStage.THINKING, content=res_struct.content,
                                       title=res_struct.title)

            iterate_agent = IterateAgent()
            messages.append(TextMessage(source="assistant", content=response.chat_message.content))

            response = await iterate_agent.on_messages(messages, cancellation_token)
            res = response.chat_message.content
            res_json = self.get_json_content(res)
            res_struct = SummaryResponse.model_validate(json_repair.loads(res_json))
            yield DeepResearchResponse(stage=DeepResearchStage.THINKING, content=res_struct.content,
                                       title=res_struct.title)

            new_plan = res_struct.content
            search_results = []

            async for output in research_teams(new_plan):
                timestamp = output.create_time.strftime('%H:%M:%S')

                # 使用if-elif确保每次只处理一种状态
                if output.stage == ResearchStage.QUERY_GENERATION:
                    logging.info(f"[{timestamp}] 第{output.iteration}轮 - 查询关键词生成:")
                    for i, query in enumerate(output.query_list, 1):
                        logging.info(f"  {i}. {query}")

                elif output.stage == ResearchStage.SEARCH_RESULT:
                    logging.info(f"[{timestamp}] 第{output.iteration}轮 - 搜索结果:")
                    for query, results in output.search_results.items():
                        repo_results = results.get("repo_search", [])
                        web_results = results.get("web_search", [])
                        for result in repo_results + web_results:
                            if result.cid not in [d.cid for d in deduplication_results]:
                                result : RetrieveChunkModel
                                if not result.web_search:
                                    result.filename = f"{result.filename}-[段{int(result.cid.split('_')[1]) + 1}]"
                                yield DeepResearchResponse(
                                    stage=DeepResearchStage.RESEARCHING,
                                    reference=result.model_dump()
                                )
                                deduplication_results.add(result)
                                search_results.append(result)  # 只添加去重后的结果

                        repo_count = len(repo_results) if not isinstance(repo_results, Exception) else 0
                        web_count = len(web_results) if not isinstance(web_results, Exception) else 0
                        logging.info(f"  查询 '{query}':")
                        logging.info(f"    - 知识库: {repo_count} 条结果")
                        logging.info(f"    - 网络搜索: {web_count} 条结果")

                elif output.stage == ResearchStage.SUMMARY:
                    logging.info(f"\n[{timestamp}] 第{output.iteration}轮 - 摘要:")
                    logging.info(f"  {output.summary}")

                elif output.stage == ResearchStage.REFLECTION:
                    logging.info(f"\n[{timestamp}] 第{output.iteration}轮 - 评估结果:")
                    status = "✓ 通过评估" if output.is_approved else "○ 需要继续研究"
                    logging.info(f"  状态: {status}")
                    # 显示反思内容预览
                    if output.reflection:
                        logging.info(f"  评估意见: {output.reflection}")

                elif output.stage == ResearchStage.COMPLETED:
                    logging.info(f"\n[{timestamp}] ✓ 研究完成！")
                    logging.info("=" * 80)
                    logging.info("\n最终研究报告:")
                    logging.info("-" * 40)
                    logging.info(output.history_summary)
                    logging.info("-" * 40)
                    report_list.append(output.history_summary)
                    break

                elif output.stage == ResearchStage.ERROR:
                    logging.info(f"\n[{timestamp}] ✗ 错误发生: {output.error_msg}")
                    break

            research_results.append(search_results)

        yield DeepResearchResponse(stage=DeepResearchStage.START_REPORTING)

        async def make_sub_report(sub_plan: str, research_report: str, results: List[RetrieveChunkModel]):
            sub_report_agent = SubReporterAgent(sub_plan=sub_plan, research_report=research_report)
            messages: Sequence[BaseChatMessage] = [
                TextMessage(source="user", content=json.dumps([{"content": chunk.plain_content, "url": chunk.url} for chunk in results], ensure_ascii=False)),
            ]

            response = await sub_report_agent.on_messages(messages, cancellation_token=cancellation_token)
            print(response.chat_message.content)
            return response.chat_message.content

        tasks = []
        for i, sub_report in enumerate(report_list):
            results = research_results[i]
            tasks.append(make_sub_report(sub_plan=plan.plan[i], research_report=sub_report, results=results))

        sub_reports = await asyncio.gather(*tasks, return_exceptions=False)
        print(sub_reports)

        reporter_agent = ReporterAgent(
            user=self.user_prompt,
            list_of_sub_reports=sub_reports
        )
        messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content="现在，请以首席分析师的身份，开始执行你的综合分析任务。"),
        ]
        cancellation_token = CancellationToken()

        response = await reporter_agent.on_messages(messages, cancellation_token=cancellation_token)
        print(response.chat_message.content)
        yield DeepResearchResponse(stage=DeepResearchStage.REPORTING, content=response.chat_message.content)


if __name__ == '__main__':
    async def plan_pipeline():
        plan_workflow = DeepResearchPlanWorkflow(user_prompt="奔驰汽车型号发展")
        async for response in plan_workflow.run_stream():
            print(response)


    async def search_pipeline():
        plan = """
(1) 查找梅赛德斯-奔驰品牌的创立背景，包括创始人卡尔·本茨（Karl Benz）和戈特利布·戴姆勒（Gottlieb Daimler）在1886年推出首辆汽车Mercedes 35 PS的过程，以及早期里程碑如1926年戴姆勒-奔驰合并后形成的标志性型号（如W01系列）的发展起源。  
(2) 梳理关键历史型号的演进序列，涵盖从经典时代（如1954年的300SL "Gullwing"、1965年的S-Class W108）到现代主流系列（如C-Class、E-Class、S-Class）的变迁，重点提取技术创新（如1980年代ABS系统的引入）和市场大事记（如S-Class在全球的销量突破事件）。  
(3) 分析技术层面的突破路径，包括引擎类型从传统汽油/柴油（如M系列引擎）向节能高效和电动化（如2019年推出EQC电动车、EQE/EQS系列）的转型，同时融入设计、安全（如PRE-SAFE系统）及性能升级（AMG高性能版本）的具体案例数据。  
(4) 研究当前产品线布局与市场生态，聚焦在轿车（A-Class、S-Class）、SUV（GLC、GLE）、及电动车型号（EQ系列）的销售表现（如2022年全球市场份额和关键区域数据），并分析竞争环境（如与宝马、奥迪的对比）及政策影响（如欧盟排放法规）。  
(5) 探索未来发展战略与挑战，包括电动化和智能化趋势下新一代型号（如Vision EQXX概念车、全电动AMG平台）的研发布局，以及面临的机遇（如可持续材料应用）和挑战（如软件系统漏洞或供应链风险）。
"""
        search_workflow = DeepResearchSearchWorkflow(user_prompt="特斯拉的发展历程和未来展望", plan=plan)
        async for response in search_workflow.run_stream():
            print(response)


    asyncio.run(search_pipeline())
