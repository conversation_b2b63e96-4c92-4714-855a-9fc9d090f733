#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

from sqlalchemy import select, update

from engine.rdb import g, session_maker_sync, query_order, paginator, fetch_all, fetch_one
from common.time import now_datetime
from model.auth import TenantModel
from model.plan import TenantPlanModel, PlanModel
from controller.system.plan import Plan, RefreshType
from common.time import ceil_to_next_day
from exception import NotFoundError


class TenantController:
    @staticmethod
    def get_query(tenant_id: int = None, match: str = None, order_by: str = None):
        where = [TenantModel.is_delete == 0]
        if tenant_id is not None:
            where.append(TenantModel.id == tenant_id)
        if match:
            where.append(TenantModel.name.ilike(f"%{match}%"))

        query = (
            select(
                TenantModel.id.label("tenant_id"),
                TenantModel.name,
                TenantModel.create_time,
                TenantModel.plan_id
            )
            .where(*where)
        )

        query = query_order(query=query, order_by=order_by, table=TenantModel)

        return query

    @staticmethod
    async def get_refresh_tenant_plan():
        query = (
            select(
                TenantPlanModel.id.label("tenant_plan_id"),
                TenantPlanModel.plan_id,
                TenantPlanModel.tenant_id,
                TenantPlanModel.token_limit,
                TenantPlanModel.refresh_time,
                TenantPlanModel.expire_time,
                TenantPlanModel.remain_refresh_count,
                TenantModel.name.label("tenant_name"),
                PlanModel.name.label("plan_name"),
                PlanModel.refresh_type,
                PlanModel.refresh_period_days,
                PlanModel.refresh_count)
            .join(TenantModel, TenantPlanModel.tenant_id == TenantModel.id)
            .join(PlanModel, TenantPlanModel.plan_id == PlanModel.id)
            .where(
                TenantPlanModel.refresh_time <= now_datetime(),
                TenantPlanModel.remain_refresh_count > 0,
                # PlanModel.refresh_type == RefreshType.period
                TenantPlanModel.is_delete == 0,
                TenantModel.is_delete == 0
            )
        )
        return await fetch_all(query)

    async def get_one(self, tenant_id: int):
        query = self.get_query(tenant_id=tenant_id)
        return await fetch_one(query=query)

    async def get_list(self, match: str = None, page: int = 1, per_page: int = 20, order_by: str = None):
        query = self.get_query(match=match, order_by=order_by)
        pager, tenants = await paginator(query=query, page=page, per_page=per_page)
        return pager, tenants

    @staticmethod
    async def get_activate_tenant_plan(tenant_id: int):
        assert tenant_id, "租户ID禁止为空"

        query = (
            select(
                TenantPlanModel.id.label("tenant_plan_id"),
                TenantPlanModel.plan_id,
                TenantPlanModel.tenant_id,
                TenantPlanModel.token_limit,
                TenantPlanModel.token_used,
                TenantPlanModel.create_time,
                TenantPlanModel.refresh_time,
                TenantPlanModel.expire_time,
                TenantPlanModel.remain_refresh_count)
            .join(TenantModel, TenantPlanModel.tenant_id == TenantModel.id)
            .join(PlanModel, TenantPlanModel.plan_id == PlanModel.id)
            .where(
                TenantModel.is_delete == 0,
                TenantModel.id == tenant_id,
                TenantPlanModel.plan_id == TenantModel.plan_id)
            .order_by(TenantPlanModel.id.desc())
        )
        res = await fetch_one(query=query)
        if not res:
            raise NotFoundError("未找到有效套餐")
        return res

    async def create(self, name: str, plan_id: int):
        plan = await Plan.get_tenant_one(plan_id)
        if not plan:
            raise ValueError(f"套餐不存在")

        tenant = TenantModel(name=name, plan_id=plan_id)
        g.session.add(tenant)
        await g.session.flush()

        tenant_id = tenant.id
        await self.create_plan(tenant_id=tenant_id, plan_id=plan_id)

        return tenant_id

    @staticmethod
    async def update(tenant_id: int, name: str = None, plan_id: int = None):
        update_info = {}
        if name is not None:
            update_info[TenantModel.name] = name
        if plan_id is not None:
            update_info[TenantModel.plan_id] = plan_id

        query = (update(TenantModel)
                 .where(TenantModel.id == tenant_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def update_plan(tenant_plan_id: int, used_add: int):
        query = (update(TenantPlanModel)
                 .where(TenantPlanModel.id == tenant_plan_id)
                 .values({TenantPlanModel.token_used: TenantPlanModel.token_used + used_add}))
        await g.session.execute(query)

    async def refresh_plan(self, tenant_plan: dict):
        # 本期不是最后一期
        if tenant_plan["remain_refresh_count"] - 1 != 0:
            refresh_time = ceil_to_next_day(dt=tenant_plan["refresh_time"] + datetime.timedelta(days=tenant_plan["refresh_period_days"]))
        # 是最后一期
        else:
            refresh_time = None

        new_tenant_plan = TenantPlanModel(
            tenant_id=tenant_plan["tenant_id"], plan_id=tenant_plan["plan_id"], token_limit=tenant_plan["token_limit"],
            expire_time=tenant_plan["expire_time"], refresh_time=refresh_time, token_used=0,
            remain_refresh_count=tenant_plan["remain_refresh_count"]-1)
        g.session.add(new_tenant_plan)
        await g.session.flush()

        await self.delete_plan(tenant_plan_id=tenant_plan["tenant_plan_id"])

        return new_tenant_plan

    @staticmethod
    async def delete(tenant_id: int):
        query = (update(TenantModel)
                 .where(TenantModel.id == tenant_id)
                 .values({TenantModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    def get_plan_query(tenant_id: int = None, is_delete: bool = True, order_by: str = None):
        where = []
        if is_delete is not None:
            where.append(TenantPlanModel.is_delete == int(is_delete))
        if tenant_id:
            where.append(TenantPlanModel.tenant_id == tenant_id)

        query = (
            select(
                TenantPlanModel.id.label("tenant_plan_id"),
                TenantPlanModel.tenant_id,
                TenantPlanModel.plan_id,
                TenantPlanModel.token_limit,
                TenantPlanModel.token_used,
                TenantPlanModel.expire_time,
                TenantPlanModel.refresh_time,
                TenantPlanModel.remain_refresh_count,
                TenantPlanModel.create_time,
                PlanModel.name.label("plan_name"),
                PlanModel.refresh_type,
                PlanModel.refresh_period_days,
                PlanModel.refresh_count,
            )
            .join(PlanModel, TenantPlanModel.plan_id == PlanModel.id, isouter=True)
            .where(*where)
        )

        query = query_order(query=query, order_by=order_by, table=TenantPlanModel)

        return query

    @staticmethod
    async def get_plan_list(tenant_id: int = None, is_delete: bool = False, page: int = 1, per_page: int = 20,
                            order_by: str = None):
        query = TenantController.get_plan_query(tenant_id=tenant_id, is_delete=is_delete, order_by=order_by)
        return await paginator(query=query, page=page, per_page=per_page)

    @staticmethod
    async def get_plan_all(tenant_id: int):
        query = TenantController.get_plan_query(tenant_id=tenant_id, is_delete=False)
        return await fetch_all(query=query)

    async def get_plan_activate_stats(self, tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        activate_tenant_plan = await self.get_activate_tenant_plan(tenant_id=tenant_id)

        return {
            "tenant_token_limit": activate_tenant_plan["token_limit"],
            "tenant_token_used": activate_tenant_plan["token_used"],
            "plan_refresh_time": activate_tenant_plan["refresh_time"],
            "plan_period_start_time": activate_tenant_plan["create_time"].replace(hour=0, minute=0, second=0, microsecond=0)
        }

    @staticmethod
    async def create_plan(tenant_id: int, plan_id: int):
        plan = await Plan.get_tenant_one(plan_id)
        if not plan:
            raise ValueError(f"套餐不存在")

        # 修改为当天的00:00:00
        plan_start_time = now_datetime().replace(hour=0, minute=0, second=0, microsecond=0)

        # 周期套餐
        if plan["refresh_type"] == RefreshType.period:
            expire_time = plan_start_time + datetime.timedelta(days=plan["refresh_period_days"]) * plan["refresh_count"]
            refresh_time = plan_start_time + datetime.timedelta(days=plan["refresh_period_days"])
            remain_refresh_count = plan["refresh_count"]-1
        # 一次性套餐
        else:
            expire_time = None
            refresh_time = None
            remain_refresh_count = 0

        tenant_plan = TenantPlanModel(
            tenant_id=tenant_id, plan_id=plan_id, token_limit=plan["tokens"], expire_time=expire_time,
            refresh_time=refresh_time, remain_refresh_count=remain_refresh_count)
        g.session.add(tenant_plan)
        await g.session.flush()

        return tenant_plan.id

    @staticmethod
    async def delete_plan(tenant_plan_id: int):
        query = (update(TenantPlanModel)
                 .where(TenantPlanModel.id == tenant_plan_id)
                 .values({TenantPlanModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            tenant = TenantModel(id=1, name="默认租户")
            session.add(tenant)

            tenant_plan = TenantPlanModel(tenant_id=1, plan_id=1, token_limit=0)
            session.add(tenant_plan)
            session.commit()


Tenant = TenantController()


if __name__ == '__main__':
    import asyncio
    from engine.rdb import session_maker
    g.session = session_maker()
    print(asyncio.run(Tenant.get_activate_tenant_plan(tenant_id=1)))
