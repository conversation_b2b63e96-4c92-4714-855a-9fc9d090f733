#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, model_validator
from lxml import etree, html as lxmlhtml
from transformers import AutoTokenizer
from langchain_text_splitters import RecursiveCharacterTextSplitter

from .tokenizer import BGE_M3_TOKENIZER


class TitleType(str, Enum):
    h1 = "h1"
    h2 = "h2"
    h3 = "h3"
    h4 = "h4"
    h5 = "h5"
    h6 = "h6"
    strong = "strong"
    span_strong = "span_strong"


class ChunkType(str, Enum):
    paragraph = "paragraph"
    toc = "toc"
    doc_title = "doc_title"


class ChunkNode(BaseModel):
    tag: str
    plain_content: str
    html_content: str
    token_counts: int
    title_type: TitleType = None

    origin_plain_content: Optional[str] = None
    xpath: str | None = None
    bboxes: List[dict] | None = None


    @model_validator(mode="after")
    def tag_lower(self):
        self.tag = self.tag.lower()
        return self

    @model_validator(mode="after")
    def analyze_title_type(self):
        if self.tag.startswith("h") and self.tag in TitleType.__members__.values():
            self.title_type = TitleType(self.tag)
        elif len(self.plain_content) <= 24 and self.tag == "strong":
            self.title_type = TitleType.strong
        elif len(self.plain_content) <= 24 and self.tag == "span" and "strong" in self.html_content:
            self.title_type = TitleType.span_strong
        else:
            pass
        return self

    @model_validator(mode="after")
    def complete_origin_plain_content(self):
        if self.origin_plain_content is None:
            self.origin_plain_content = self.plain_content
        return self


class Chunk(BaseModel):
    nodes: List[ChunkNode] = []
    title: List[str] | None = []

    token_counts: int = 0
    plain_content: str = ""
    origin_plain_content: str = ""
    vector: List[int] = None
    start_offset: int = None
    end_offset: int = None
    bboxes: List[dict] = []
    type_: str | ChunkType = ChunkType.paragraph


    def add_node(self, node: ChunkNode):
        self.token_counts += node.token_counts

        self.nodes.append(node)

        if node.bboxes:
            self.bboxes.extend(node.bboxes)

        self.origin_plain_content += node.origin_plain_content

        if (
                self.type_ == ChunkType.paragraph
                and (len(self.title) < 3
                and node.title_type is not None
                and ((not self.nodes) or (self.nodes[-1]).title_type is not None))
                and node.plain_content not in self.title):
            self.title.append(node.plain_content)
        else:
            self.plain_content += node.plain_content.strip()

        # 对于不同的标签,或新标签内容为空白时,视为换行.追加换行符
        # 否则不追加换行符
        if self.nodes:
            if self.nodes[-1].tag != node.tag or node.origin_plain_content.strip() == "":
                self.origin_plain_content += "\n"
                self.plain_content += "\n"


    @model_validator(mode="after")
    def init_fix(self):
        # 避免前置方法默认值传值造成的问题
        if self.title is None:
            self.title = []

        return self


class Chunker:
    replace_one_white_pattern = re.compile(r"(?<![a-zA-Z\W0-9])\s(?![a-zA-Z\W0-9])")
    remove_html_blank = re.compile(r'(?<=>)\s+(?=<)', )

    def __init__(self,
                 tokenizer: AutoTokenizer = BGE_M3_TOKENIZER,
                 max_tokens: int = 1024,
                 max_tolerance_tokens: int = 2048):

        self.max_tokens = max_tokens
        self.max_tolerance_tokens = max_tolerance_tokens
        self.tokenizer = tokenizer

        self.chunks: list[Chunk] = []
        self._last_node: ChunkNode | None = None

    def chunk(self, **kwargs) -> List[Chunk]:...

    def add_splittable_symbol(self):
        """如果前个chunk的尾节点,不等于后个chunk的首节点,则在前个chunk的尾节点追加换行符.主要为全文档纯文本构造做准备"""
        for i, chunk in enumerate(self.chunks):
            if i == 0:
                continue
            if self.chunks[i-1].nodes[-1].tag != chunk.nodes[0].tag and self.chunks[i-1].plain_content[-1] != "\n":
                self.chunks[i-1].origin_plain_content += "\n"

    def assign_chunk_offsets(self):
        """
        根据plain_content计算并分配每一个chunk的start_offset和end_offset
        简单地按照chunk的顺序累计计算偏移量
        """
        if not self.chunks:
            return

        current_offset = 0

        for i, chunk in enumerate(self.chunks):
            chunk_content = chunk.origin_plain_content
            chunk_length = len(chunk_content)

            # 设置当前chunk的偏移量
            chunk.start_offset = current_offset
            chunk.end_offset = current_offset + chunk_length

            # 更新下一个chunk的起始位置
            current_offset = chunk.end_offset

    def add_node(self, tag: str, html_content: str, plain_content: str, token_counts: int,
                 xpath: str = None, bboxes: List[dict] = None, origin_plain_content: str = None,
                 title: List[str] = None, type_: ChunkType | str = ChunkType.paragraph):
        """
        将节点添加到当前块或如果节点可以分割则创建新块。该方法对纯文本内容进行编码以计算token数量，
        判断块是否可分割，并适当地将节点添加到最后一个块或新创建的块中。

        参数:
            tag (str): 要添加的节点的标签名称。
            html_content (str): 节点内容的HTML表示。
            plain_content (str): 节点内容的纯文本表示。
            token_counts (int): 节点内容的token数量。
            xpath (str): 文档中节点的XPath位置。
            bboxes (List[dict]): 节点内容的bbox信息,用于后续定位标记
            origin_plain_content (str): 原始文本,仅用来做记录和拼接正文.不存在时取plain_content
            type_: 所允许的chunk_type类型,以做切分和筛选
        """
        node = ChunkNode(
            tag=tag, plain_content=plain_content, html_content=html_content, token_counts=token_counts, xpath=xpath,
            bboxes=bboxes, origin_plain_content=origin_plain_content
        )

        if (self._is_chunk_splittable(node=node)
                or self._last_node is None
                or type_ != self.chunks[-1].type_):
            self.chunks.append(Chunk(title=title, type_=type_))

        self._last_chunk.add_node(node)

        self._last_node = node

    def chunk_large_table_tag(self, element: etree.Element, root: etree.ElementTree):
        """
        智能处理表格：识别列头，按行处理，包含合并单元格信息
        """
        # 获取所有行
        all_rows = element.xpath('.//tr')
        if not all_rows:
            return

        # 按列数分组，形成逻辑块
        column_blocks = self._group_rows_by_column_count(all_rows)

        # 获取table xpath用于计算绝对位置
        table_xpath = root.getpath(element)

        # 处理每个列数块
        for block_rows in column_blocks:
            if not block_rows:
                continue

            # 在当前块内识别列头
            header_row, header_columns = self._detect_block_headers(block_rows)

            # 获取当前块的数据行（排除列头行）
            data_rows = [row for row in block_rows if row != header_row and self._is_data_row(row)]

            if not data_rows:
                continue

            # 为当前块的每一行创建独立的节点
            for row in data_rows:
                # 构建包含列头和当前行的table HTML
                table_html = self._build_row_table_html(header_row, row)
                table_html = self.remove_html_blank.sub("", table_html)

                # 提取结构化纯文本（列头=内容格式）
                structured_text = self._extract_row_structured_text(header_columns, row, data_rows)

                # 计算token数量
                token_counts = len(self.tokenizer.encode(text=table_html))

                # 获取当前行在整个table中的绝对位置
                try:
                    tr_index = all_rows.index(row) + 1  # xpath索引从1开始
                    row_xpath = f"{table_xpath}/tr[{tr_index}]"
                except (ValueError, AttributeError):
                    # 如果获取失败，使用table元素的xpath作为备选
                    row_xpath = table_xpath

                if structured_text.strip():  # 只有当有内容时才添加节点
                    self.add_node(
                        tag="table_part",
                        xpath=row_xpath,
                        html_content=table_html,
                        plain_content=structured_text,
                        origin_plain_content=structured_text + "\n",
                        token_counts=token_counts
                    )

    def _group_rows_by_column_count(self, all_rows):
        """
        按列数分组行，形成逻辑块
        返回: List[List[row]] - 每个子列表包含相同列数的行
        """
        if not all_rows:
            return []

        # 计算每行的有效列数（考虑colspan）
        def get_effective_column_count(row):
            cells = row.xpath('.//td | .//th')
            if not cells:
                return 0

            total_cols = 0
            for cell in cells:
                colspan = int(cell.get('colspan', 1))
                total_cols += colspan
            return total_cols

        # 按列数分组
        column_groups = {}
        for row in all_rows:
            col_count = get_effective_column_count(row)
            if col_count > 0:  # 只处理有内容的行
                if col_count not in column_groups:
                    column_groups[col_count] = []
                column_groups[col_count].append(row)

        # 返回按列数排序的分组（列数多的在前）
        sorted_groups = []
        for col_count in sorted(column_groups.keys(), reverse=True):
            sorted_groups.append(column_groups[col_count])

        return sorted_groups

    def _detect_block_headers(self, block_rows):
        """
        在指定的行块内识别列头
        返回: (header_row, header_columns)
        """
        if not block_rows:
            return None, []

        # 检查块内的前几行，寻找列头
        for i, row in enumerate(block_rows[:10]):  # 检查前10行
            cells = row.xpath('.//td | .//th')
            if not cells:
                continue

            # 优先检测多列的列头行（排除单列分隔行）
            if len(cells) == 1:
                # 单列行，检查是否为分隔行
                cell = cells[0]
                if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
                    continue  # 跳过分隔行

            # 检查是否所有单元格都包含加粗标签
            bold_count = 0
            total_cells = 0
            header_columns = []

            for cell in cells:
                cell_text = cell.text_content().strip()
                if cell_text:  # 只计算有内容的单元格
                    total_cells += 1
                    # 检查是否包含加粗标签
                    if cell.xpath('.//b') or cell.xpath('.//strong'):
                        bold_count += 1
                    header_columns.append(cell_text)

            # 如果大部分单元格都加粗，且有多列，认为是列头行
            if total_cells > 1 and bold_count >= total_cells * 0.8:
                return row, header_columns

        # 如果没找到明显的列头，使用第一个数据行作为参考
        for row in block_rows:
            if self._is_data_row(row):
                cells = row.xpath('.//td | .//th')
                header_columns = [f"列{i+1}" for i in range(len(cells))]
                return None, header_columns

        return None, []

    def _detect_table_headers(self, all_rows):
        """
        智能识别表格列头
        返回: (header_row, header_columns)
        """
        if not all_rows:
            return None, []

        # 检查前几行，寻找列头
        for i, row in enumerate(all_rows[:15]):  # 检查前15行
            # 跳过分隔行
            if self._is_separator_row(row):
                continue

            # 检查是否为列头行：所有单元格都加粗，且不是分隔行
            cells = row.xpath('.//td | .//th')
            if not cells:
                continue

            # 排除分隔行（只有一个单元格且有colspan）
            if len(cells) == 1 and cells[0].get('colspan'):
                continue

            # 检查是否所有单元格都包含加粗标签
            bold_count = 0
            total_cells = 0
            header_columns = []

            for cell in cells:
                cell_text = cell.text_content().strip()
                if cell_text:  # 只计算有内容的单元格
                    total_cells += 1
                    # 检查是否包含加粗标签
                    if cell.xpath('.//b') or cell.xpath('.//strong'):
                        bold_count += 1
                        header_columns.append(cell_text)
                    else:
                        header_columns.append(cell_text)

            # 如果大部分单元格都加粗，且有多列，认为是列头行
            if total_cells > 3 and bold_count >= total_cells * 0.8:
                return row, header_columns

        # 如果没找到明显的列头，使用第一个数据行作为参考
        for row in all_rows:
            if self._is_data_row(row):
                cells = row.xpath('.//td | .//th')
                header_columns = [f"列{i+1}" for i in range(len(cells))]
                return None, header_columns

        return None, []

    def _is_separator_row(self, row):
        """判断是否为分隔行"""
        cells = row.xpath('.//td | .//th')
        if len(cells) == 1:
            # 只有一个单元格且有colspan属性，可能是分隔行
            cell = cells[0]
            if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
                return True
        return False

    def _is_data_row(self, row):
        """判断是否为数据行"""
        # 排除分隔行
        if self._is_separator_row(row):
            return False

        # 检查是否有实际内容
        cells = row.xpath('.//td | .//th')
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text and cell_text not in ['', ' ', '\n']:
                return True
        return False

    def _build_row_table_html(self, header_row, data_row):
        """构建包含列头和数据行的table HTML"""
        table_parts = []

        # 添加表头（如果存在）
        if header_row is not None:
            from lxml import html as lxmlhtml
            header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
            table_parts.append(header_html)

        # 添加数据行
        from lxml import html as lxmlhtml
        row_html = lxmlhtml.tostring(data_row, encoding="unicode", pretty_print=False)
        table_parts.append(row_html)

        # 组合成完整的table
        tbody_content = "".join(table_parts)
        return f"<table><tbody>{tbody_content}</tbody></table>"

    def _extract_row_structured_text(self, header_columns, row, all_data_rows):
        """
        提取行的结构化纯文本，格式：列头=内容
        处理合并单元格的情况
        """
        if not row:
            return ""

        # 获取当前行的所有单元格
        cells = row.xpath('.//td | .//th')
        if not cells:
            return ""

        # 构建结构化文本
        text_parts = []

        # 如果没有列头，直接使用单元格内容
        if not header_columns:
            for i, cell in enumerate(cells):
                cell_value = cell.text_content().strip()
                if cell_value:
                    text_parts.append(f"内容{i+1}={cell_value}")
            return " ".join(text_parts)

        # 处理合并单元格：需要找到前面行中的rowspan单元格
        merged_cell_values = self._get_merged_cell_values(row, all_data_rows)

        # 当前行的单元格索引
        current_cell_index = 0

        # 如果当前行的列数与列头数不匹配，使用简化处理
        total_cell_cols = sum(int(cell.get('colspan', 1)) for cell in cells)
        if len(header_columns) != total_cell_cols:
            # 简化处理：直接使用单元格内容
            for i, cell in enumerate(cells):
                cell_value = cell.text_content().strip()
                if cell_value:
                    if i < len(header_columns):
                        text_parts.append(f"{header_columns[i]}={cell_value}")
                    else:
                        text_parts.append(f"内容{i+1}={cell_value}")

            # 添加合并单元格的内容
            for col_index, merged_value in merged_cell_values.items():
                if col_index < len(header_columns):
                    text_parts.append(f"{header_columns[col_index]}={merged_value}")

            return " ".join(text_parts)

        # 标准处理：优先使用当前行的单元格，合并单元格作为补充
        current_cell_index = 0

        for col_index, header in enumerate(header_columns):
            cell_value = ""

            # 优先使用当前行的单元格
            if current_cell_index < len(cells):
                cell = cells[current_cell_index]
                cell_value = cell.text_content().strip()

                # 检查colspan，如果有则跳过后续列
                colspan = int(cell.get('colspan', 1))
                current_cell_index += 1

                # 如果有colspan，为后续列也使用相同的值
                if colspan > 1:
                    for i in range(1, colspan):
                        if col_index + i < len(header_columns):
                            next_header = header_columns[col_index + i]
                            if cell_value:
                                text_parts.append(f"{next_header}={cell_value}")

            # 如果当前行没有对应单元格，检查是否有来自前面行的合并单元格
            elif col_index in merged_cell_values:
                cell_value = merged_cell_values[col_index]

            # 添加当前列的内容
            if cell_value:
                text_parts.append(f"{header}={cell_value}")

        return " ".join(text_parts)

    def _get_merged_cell_values(self, current_row, all_data_rows):
        """获取影响当前行的合并单元格值"""
        merged_values = {}

        try:
            current_index = all_data_rows.index(current_row)
        except ValueError:
            return merged_values

        # 检查前面的行是否有rowspan影响当前行
        for i in range(max(0, current_index - 10), current_index):  # 最多检查前10行
            prev_row = all_data_rows[i]
            prev_cells = prev_row.xpath('.//td | .//th')

            col_offset = 0
            for cell in prev_cells:
                rowspan = int(cell.get('rowspan', 1))
                colspan = int(cell.get('colspan', 1))

                # 如果rowspan影响到当前行
                if i + rowspan > current_index:
                    cell_value = cell.text_content().strip()
                    if cell_value:
                        # 为所有受影响的列设置值
                        for c in range(colspan):
                            merged_values[col_offset + c] = cell_value

                col_offset += colspan

        return merged_values

    def _analyze_merged_cells_and_group(self, data_rows):
        """分析合并单元格并分组相关行"""
        if not data_rows:
            return []

        groups = []
        processed_rows = set()

        for i, row in enumerate(data_rows):
            if i in processed_rows:
                continue

            # 找到当前行涉及的所有行（由于rowspan）
            group_rows = [row]
            max_rowspan = 1

            # 检查当前行的所有单元格的rowspan
            for cell in row.xpath('.//td'):
                rowspan = int(cell.get('rowspan', 1))
                max_rowspan = max(max_rowspan, rowspan)

            # 添加受rowspan影响的后续行
            for j in range(i + 1, min(i + max_rowspan, len(data_rows))):
                if j not in processed_rows:
                    group_rows.append(data_rows[j])
                    processed_rows.add(j)

            processed_rows.add(i)
            groups.append(group_rows)

        return groups

    def _build_complete_table_html(self, header_rows, group_rows):
        """构建完整的table HTML"""
        table_parts = []

        # 添加表头
        if header_rows:
            for header_row in header_rows:
                header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
                table_parts.append(header_html)

        # 添加数据行
        for row in group_rows:
            row_html = lxmlhtml.tostring(row, encoding="unicode", pretty_print=False)
            table_parts.append(row_html)

        # 组合成完整的table
        tbody_content = "".join(table_parts)
        return f"<table><tbody>{tbody_content}</tbody></table>"

    def _extract_structured_text(self, column_headers, group_rows):
        """提取结构化纯文本，格式：行头 列头：内容，确保每个单元格只被提取一次"""
        if not group_rows:
            return ""

        # 提取行头（第一列的内容，通常是主要标识）
        row_header = ""
        first_row = group_rows[0]
        first_cells = first_row.xpath('.//td')
        if first_cells:
            row_header = first_cells[0].text_content().strip()

        # 构建结构化文本
        text_parts = []

        # 添加行头作为主标识
        if row_header:
            text_parts.append(row_header)

        # 记录已处理的单元格，避免重复提取
        processed_cells = set()

        # 收集所有未处理的单元格内容（简化处理，不按列分类）
        all_content = []
        for row in group_rows:
            for cell in row.xpath('.//td'):
                cell_id = id(cell)
                if cell_id not in processed_cells:
                    content = cell.text_content().strip()
                    if content and content != row_header:
                        all_content.append(content)
                        processed_cells.add(cell_id)

        # 添加所有内容
        if all_content:
            text_parts.extend(all_content)

        return " ".join(text_parts)






    def chunk_large_text_tag(self, element: etree.Element, root: etree.ElementTree):
        """
        对于超长文本标签：先提取出标签下的纯文本，进行纯文本切分，然后将切分后的纯文本重新包装为html标签

        Args:
            element:
            root:

        Returns:

        """
        # 提取元素下的纯文本
        plain_text = self._extract_plain_text(element)

        # 使用文本分割器进行切分
        text_splitter = RecursiveCharacterTextSplitter(
            separators=["\n\n", "\n", "。", "！", "!", "？", "?", "，", " "],
            chunk_size=self.max_tokens,
            chunk_overlap=0
        )

        # 分割文本
        text_chunks = text_splitter.split_text(plain_text)
        # 获取原始标签名和xpath
        tag = element.tag.lower()
        xpath = root.getpath(element)

        # 为每个文本块创建HTML标签并添加节点
        for chunk_text in text_chunks:
            # 重新包装为HTML标签
            html_content = f"<{tag}>{chunk_text}</{tag}>"

            # 计算token数量
            token_counts = len(self.tokenizer.encode(text=html_content))

            # 添加节点
            self.add_node(
                tag=f"{tag}_part",
                xpath=xpath,
                html_content=html_content,
                plain_content=chunk_text,
                token_counts=token_counts
            )

    def _is_chunk_splittable(self, node: ChunkNode) -> bool:
        if not self.chunks:
            return False

        # token长度超过设定值,且上个节点包含正文
        if (self._last_chunk.token_counts + node.token_counts > self.max_tokens
                and self._last_chunk.plain_content.strip()):
            return True

        # 上一个节点非标题,但此节点为标题,进行切分
        if self._last_node and self._last_node.title_type is None and node.title_type:
            return True

        # 此节点为大节点拆分的行
        if node.tag.endswith("part"):
            return True

        return False

    def _extract_plain_text(self, element: etree.Element):
        element_fragments = []
        elements = [element] if not isinstance(element, list) else element
        for e in elements:
            for text in e.xpath('.//text()[normalize-space() != ""]'):
                text = self.replace_one_white_pattern.sub("", text.strip())
                element_fragments.append(text)

        return " ".join(element_fragments).replace("\n", "")

    @property
    def _last_chunk(self) -> Chunk | None:
        if not self.chunks:
            return None
        return self.chunks[-1]
