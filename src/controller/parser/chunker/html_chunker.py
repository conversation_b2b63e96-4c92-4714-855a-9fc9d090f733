#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from lxml import etree, html as lxmlhtml
from transformers import AutoTokenizer

from common.logger import logger
from controller.parser.chunker import Chun<PERSON>, ChunkNode


class HtmlChunker(Chunker):
    def __init__(self,
                 tokenizer: AutoTokenizer,
                 max_tokens: int = 1024,
                 max_tolerance_tokens: int = 2048):
        super().__init__(max_tokens=max_tokens, max_tolerance_tokens=max_tolerance_tokens, tokenizer=tokenizer)

    def chunk(self, html_content: str):
        self.parsing(html_content=html_content)
        self.add_splittable_symbol()
        self.assign_chunk_offsets()

        return self.chunks

    def parsing(self, html_content):
        tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
        if tree is None:
            raise ValueError("html_content is not a valid html string")
        root = tree.getroottree()  # 用于寻找xpath

        body_tag = tree.xpath('//body')[0]
        context = etree.iterwalk(body_tag, events=("start",))

        for action, element in context:
            tag = element.tag.lower()
            element_text = element.text_content()

            # 过滤空文本节点
            if not element_text.strip():
                continue

            # 无视内部子节点,直接可从以下节点提取所有文本信息的
            # 每个节点提取
            if (tag in ("table", "p", "a", "span", "li", "i", "strong", "h1", "h2", "h3", "h4", "h5", "h6", "em", "b",
                        "dt", "code")
                    # 某些惯用父级的标签,只有在无子节点时才进行处理
                    or (len(element) == 0 and tag in ("div", "center"))
                    # 部分可能对正文无关的标签,只在字数较长时才进行处理
                    or (tag in ("button", "option", "sub") and len(element_text) > 5)):
                fragments = [
                    self.replace_one_white_pattern.sub("", text.strip())
                    for text in element.xpath('.//text()[normalize-space() != ""]')
                ]
                plain_content = " ".join(fragments).replace("\n", "")

                # 替换element中的"None"为空格
                element = self._remove_style_attributes(element)
                html_content = lxmlhtml.tostring(element, encoding="unicode", pretty_print=False)
                html_content = self.remove_html_blank.sub("", html_content)

                xpath = root.getpath(element)
                token_counts = len(self.tokenizer.encode(text=html_content))
                if token_counts <= self.max_tolerance_tokens:
                    self.add_node(
                        tag=tag, xpath=xpath, html_content=html_content, plain_content=plain_content,
                        token_counts=token_counts)
                else:
                    logger.warning(
                        f"标签[{tag}] tokens [{token_counts}] 已超过最大容忍值 [{self.max_tolerance_tokens}] 按照标签进行细粒度处理")
                    # 当table的tokens超过长度时，跳过并记录th列头，并将每一行的正文的html和列头拼凑
                    if tag == "table":
                        # 对于超长表格,单独按行处理
                        print(token_counts)
                        self.chunk_large_table_tag(element=element, root=root)
                    else:
                        # 对于其他超长标签，使用文本切分方法
                        self.chunk_large_text_tag(element=element, root=root)
                # 子节点全部从内存中删除
                for child in element:
                    element.remove(child)
                continue

            if len(element) == 0:
                # 这里button、sub、option
                if tag in ("script", "style", "button", "option", "sub"):
                    pass

                else:
                    # 用于检查逻辑遗漏
                    logger.error(f"有未命中的节点"
                                 f"Tag: {tag}, "
                                 f"Text: {element.text_content()} "
                                 f"Xpath: {root.getpath(element)} "
                                 f'HTML: {lxmlhtml.tostring(element, encoding="unicode", pretty_print=True)} ')


    def _remove_style_attributes(self, element):
        """递归移除元素及其子元素的所有样式相关属性"""
        # 定义样式相关属性列表
        style_attrs = [
            "style", "class", "id", "align", "width", "height", "bgcolor", "color", "font", "margin", "padding",
            "border", "cellpadding", "cellspacing", "valign", "background", "face", "size", "text-align"
        ]

        # 从当前元素中移除样式属性
        for attr in style_attrs:
            if attr in element.attrib:
                del element.attrib[attr]

        # 递归处理所有子元素
        for child in element:
            self._remove_style_attributes(child)

        return element


if __name__ == '__main__':
    from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER
    with open("../1.html", mode="r", encoding="utf-8") as f:
        html = f.read()
    HtmlChunker(tokenizer=BGE_M3_TOKENIZER).parsing(html)