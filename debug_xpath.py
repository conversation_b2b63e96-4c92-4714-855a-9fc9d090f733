#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

# 读取HTML文件
with open("1.html", "r", encoding="utf-8") as f:
    html_content = f.read()

# 解析HTML
tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
root = tree.getroottree()

# 获取第3个table
tables = tree.xpath('//table')
if len(tables) >= 3:
    table3 = tables[2]  # 第3个table (索引从0开始)
    
    print(f"第3个table的xpath: {root.getpath(table3)}")
    
    # 获取所有tr元素
    all_trs = table3.xpath('.//tr')
    print(f"第3个table中总共有 {len(all_trs)} 个tr元素")
    
    # 查找包含"UI/UE 设计"的tr
    for i, tr in enumerate(all_trs):
        tr_text = tr.text_content()
        if "UI/UE" in tr_text and "设计" in tr_text:
            print(f"找到'UI/UE 设计'在第 {i+1} 个tr中")
            print(f"该tr的xpath: {root.getpath(tr)}")
            print(f"tr内容: {tr_text.strip()}")
            break
    
    # 同时检查通过xpath查询的结果
    data_rows = table3.xpath('.//tr[td]')
    print(f"\n通过'.//tr[td]'查询到 {len(data_rows)} 个tr")

    ui_ue_tr = None
    for i, tr in enumerate(data_rows):
        tr_text = tr.text_content()
        if "UI/UE" in tr_text and "设计" in tr_text:
            ui_ue_tr = tr
            print(f"在data_rows中，'UI/UE 设计'是第 {i+1} 个元素")
            print(f"在all_trs中的位置: {all_trs.index(tr) + 1}")
            break

    # 模拟我们的代码逻辑
    if ui_ue_tr is not None:
        print(f"\n=== 模拟代码逻辑 ===")

        # 模拟 group_rows[0] = ui_ue_tr
        first_row = ui_ue_tr

        # 获取table中所有tr元素
        all_trs_in_table = table3.xpath('.//tr')

        # 找到第一行在所有tr中的索引位置
        tr_index = all_trs_in_table.index(first_row) + 1  # xpath索引从1开始

        # 构建正确的xpath
        table_xpath = root.getpath(table3)
        first_row_xpath = f"{table_xpath}/tr[{tr_index}]"

        print(f"first_row: {first_row}")
        print(f"all_trs_in_table长度: {len(all_trs_in_table)}")
        print(f"tr_index: {tr_index}")
        print(f"table_xpath: {table_xpath}")
        print(f"构建的xpath: {first_row_xpath}")

        # 验证构建的xpath是否正确
        try:
            found_element = tree.xpath(first_row_xpath)[0]
            found_text = found_element.text_content()
            print(f"验证xpath找到的内容: {found_text[:50]}...")
            print(f"是否匹配: {'UI/UE' in found_text and '设计' in found_text}")
        except Exception as e:
            print(f"验证xpath时出错: {e}")
