#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')
table3 = tables[2]
all_rows = table3.xpath('.//tr')

# 查找包含'项目'、'类别'、'描述'等列头的行
for i, row in enumerate(all_rows):
    cells = row.xpath('.//td | .//th')
    row_text = row.text_content()
    if '项目' in row_text and '类别' in row_text and '描述' in row_text:
        print(f'找到列头行在第 {i+1} 行 (tr[{i+1}])')
        for j, cell in enumerate(cells):
            cell_text = cell.text_content().strip()
            has_bold = bool(cell.xpath('.//b') or cell.xpath('.//strong'))
            print(f'  列 {j+1}: "{cell_text}" 加粗: {has_bold}')
        break
