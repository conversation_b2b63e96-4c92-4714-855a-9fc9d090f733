#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')
table3 = tables[2]  # table[3]

all_rows = table3.xpath('.//tr')

# 找到"产品经理"行
product_manager_row = None
for row in all_rows:
    if '产品经理' in row.text_content():
        product_manager_row = row
        break

if product_manager_row is not None:
    print("测试修正后的逻辑:")
    
    # 列头
    header_columns = ['项目', '类别', '描述', '必选/可选', '单位', '单价（元）', '数量', '总价（元）']
    
    # 当前行的单元格
    cells = product_manager_row.xpath('.//td | .//th')
    print(f"当前行有 {len(cells)} 个单元格")
    
    # 使用修正后的逻辑
    text_parts = []
    current_cell_index = 0
    
    for col_index, header in enumerate(header_columns):
        cell_value = ""
        
        # 优先使用当前行的单元格
        if current_cell_index < len(cells):
            cell = cells[current_cell_index]
            cell_value = cell.text_content().strip()
            
            # 检查colspan
            colspan = int(cell.get('colspan', 1))
            current_cell_index += 1
            
            print(f"列{col_index} ({header}): '{cell_value}' (单元格{current_cell_index}, colspan={colspan})")
            
            # 如果有colspan，为后续列也使用相同的值
            if colspan > 1:
                for i in range(1, colspan):
                    if col_index + i < len(header_columns):
                        next_header = header_columns[col_index + i]
                        if cell_value:
                            text_parts.append(f"{next_header}={cell_value}")
        
        # 添加当前列的内容
        if cell_value:
            text_parts.append(f"{header}={cell_value}")
    
    structured_text = " ".join(text_parts)
    print(f"\n结构化文本: {structured_text}")
    
    print(f"\n期望的结果应该是:")
    print(f"项目=定制化功能 类别=产品经理 描述=针对定制化功能提供需求调研、原型设计等服务 必选/可选=可选 单位=人天 单价（元）=5,000 数量= 总价（元）=")
