#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
tables = tree.xpath('//table')
table3 = tables[2]  # table[3]

# 按列数分组
def get_effective_column_count(row):
    cells = row.xpath('.//td | .//th')
    if not cells:
        return 0
    
    total_cols = 0
    for cell in cells:
        colspan = int(cell.get('colspan', 1))
        total_cols += colspan
    return total_cols

all_rows = table3.xpath('.//tr')
column_groups = {}
for row in all_rows:
    col_count = get_effective_column_count(row)
    if col_count > 0:
        if col_count not in column_groups:
            column_groups[col_count] = []
        column_groups[col_count].append(row)

# 检查8列块的前几行
print("检查8列块的前几行:")
eight_col_rows = column_groups.get(8, [])
for i, row in enumerate(eight_col_rows[:10]):
    cells = row.xpath('.//td | .//th')
    print(f"\n第{i+1}行:")
    
    bold_count = 0
    total_cells = 0
    for j, cell in enumerate(cells):
        cell_text = cell.text_content().strip()
        if cell_text:
            total_cells += 1
            has_bold = bool(cell.xpath('.//b') or cell.xpath('.//strong'))
            if has_bold:
                bold_count += 1
            print(f"  单元格{j+1}: '{cell_text[:20]}...' 加粗: {has_bold}")
    
    print(f"  加粗比例: {bold_count}/{total_cells} = {bold_count/total_cells if total_cells > 0 else 0:.2f}")
    
    # 检查是否为分隔行
    if len(cells) == 1:
        cell = cells[0]
        if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
            print(f"  -> 分隔行")
    
    if bold_count > 0 and total_cells > 0 and bold_count >= total_cells * 0.8:
        print(f"  -> 可能是列头行!")
