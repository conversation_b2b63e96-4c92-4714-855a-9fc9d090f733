#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from lxml import html as lxmlhtml, etree

# 使用之前的NewChunker类
class NewChunker:
    def __init__(self):
        self.remove_html_blank = lambda x: x
    
    def _group_rows_by_column_count(self, all_rows):
        """按列数分组行，形成逻辑块"""
        if not all_rows:
            return []
        
        def get_effective_column_count(row):
            cells = row.xpath('.//td | .//th')
            if not cells:
                return 0
            
            total_cols = 0
            for cell in cells:
                colspan = int(cell.get('colspan', 1))
                total_cols += colspan
            return total_cols
        
        # 按列数分组
        column_groups = {}
        for row in all_rows:
            col_count = get_effective_column_count(row)
            if col_count > 0:  # 只处理有内容的行
                if col_count not in column_groups:
                    column_groups[col_count] = []
                column_groups[col_count].append(row)
        
        # 返回按列数排序的分组（列数多的在前）
        sorted_groups = []
        for col_count in sorted(column_groups.keys(), reverse=True):
            sorted_groups.append(column_groups[col_count])
        
        return sorted_groups

    def _detect_block_headers(self, block_rows):
        """在指定的行块内识别列头"""
        if not block_rows:
            return None, []

        # 检查块内的前几行，寻找列头
        for i, row in enumerate(block_rows[:10]):  # 检查前10行
            cells = row.xpath('.//td | .//th')
            if not cells:
                continue

            # 优先检测多列的列头行（排除单列分隔行）
            if len(cells) == 1:
                # 单列行，检查是否为分隔行
                cell = cells[0]
                if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
                    continue  # 跳过分隔行

            # 检查是否所有单元格都包含加粗标签
            bold_count = 0
            total_cells = 0
            header_columns = []

            for cell in cells:
                cell_text = cell.text_content().strip()
                if cell_text:  # 只计算有内容的单元格
                    total_cells += 1
                    # 检查是否包含加粗标签
                    if cell.xpath('.//b') or cell.xpath('.//strong'):
                        bold_count += 1
                    header_columns.append(cell_text)

            # 如果大部分单元格都加粗，且有多列，认为是列头行
            if total_cells > 1 and bold_count >= total_cells * 0.8:
                print(f"    检测到列头行: {bold_count}/{total_cells}加粗")
                return row, header_columns

        return None, []

    def _is_separator_row(self, row):
        """判断是否为分隔行"""
        cells = row.xpath('.//td | .//th')
        if len(cells) == 1:
            cell = cells[0]
            if cell.get('colspan') or cell.get('bgcolor') in ['#0066D1', '#ADB9CA']:
                return True
        return False

    def _is_data_row(self, row):
        """判断是否为数据行"""
        if self._is_separator_row(row):
            return False
        
        cells = row.xpath('.//td | .//th')
        for cell in cells:
            cell_text = cell.text_content().strip()
            if cell_text and cell_text not in ['', ' ', '\n']:
                return True
        return False

    def _get_merged_cell_values(self, current_row, all_data_rows):
        """获取影响当前行的合并单元格值"""
        merged_values = {}
        
        try:
            current_index = all_data_rows.index(current_row)
        except ValueError:
            return merged_values
        
        # 检查前面的行是否有rowspan影响当前行
        for i in range(max(0, current_index - 10), current_index):
            prev_row = all_data_rows[i]
            prev_cells = prev_row.xpath('.//td | .//th')
            
            col_offset = 0
            for cell in prev_cells:
                rowspan = int(cell.get('rowspan', 1))
                colspan = int(cell.get('colspan', 1))
                
                # 如果rowspan影响到当前行
                if i + rowspan > current_index:
                    cell_value = cell.text_content().strip()
                    if cell_value:
                        # 为所有受影响的列设置值
                        for c in range(colspan):
                            merged_values[col_offset + c] = cell_value
                
                col_offset += colspan
        
        return merged_values

    def _extract_row_structured_text(self, header_columns, row, all_data_rows):
        """提取行的结构化纯文本，格式：列头=内容"""
        if not row:
            return ""
        
        cells = row.xpath('.//td | .//th')
        if not cells:
            return ""
        
        text_parts = []
        
        # 如果没有列头，直接使用单元格内容
        if not header_columns:
            for i, cell in enumerate(cells):
                cell_value = cell.text_content().strip()
                if cell_value:
                    text_parts.append(f"内容{i+1}={cell_value}")
            return " ".join(text_parts)
        
        # 处理合并单元格
        merged_cell_values = self._get_merged_cell_values(row, all_data_rows)
        
        # 标准处理
        current_cell_index = 0
        for col_index, header in enumerate(header_columns):
            cell_value = ""
            
            # 检查是否有来自前面行的合并单元格
            if col_index in merged_cell_values:
                cell_value = merged_cell_values[col_index]
            elif current_cell_index < len(cells):
                # 使用当前行的单元格
                cell = cells[current_cell_index]
                cell_value = cell.text_content().strip()
                current_cell_index += 1
            
            # 添加当前列的内容
            if cell_value:
                text_parts.append(f"{header}={cell_value}")
        
        return " ".join(text_parts)

# 测试table[3]（包含"UI/UE 设计"的表格）
with open('1.html', 'r', encoding='utf-8') as f:
    html_content = f.read()

tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
root = tree.getroottree()
tables = tree.xpath('//table')

table3 = tables[2]  # table[3] (索引从0开始)
chunker = NewChunker()

all_rows = table3.xpath('.//tr')
print(f"Table[3]总共有 {len(all_rows)} 行")

# 按列数分组
column_blocks = chunker._group_rows_by_column_count(all_rows)
print(f"分成 {len(column_blocks)} 个列数块:")

for i, block_rows in enumerate(column_blocks):
    if not block_rows:
        continue
    
    # 计算列数
    first_row = block_rows[0]
    cells = first_row.xpath('.//td | .//th')
    col_count = sum(int(cell.get('colspan', 1)) for cell in cells)
    
    print(f"\n块 {i+1}: {col_count}列, {len(block_rows)}行")
    
    # 识别列头
    header_row, header_columns = chunker._detect_block_headers(block_rows)
    if header_row is not None:
        print(f"  列头: {header_columns}")
    else:
        print(f"  无列头")
    
    # 获取数据行
    data_rows = [row for row in block_rows if row != header_row and chunker._is_data_row(row)]
    print(f"  数据行: {len(data_rows)}行")
    
    # 测试关键行
    test_keywords = ['UI/UE', '产品经理', '前端开发']
    found_test_rows = []
    for row in data_rows:
        row_text = row.text_content()
        if any(keyword in row_text for keyword in test_keywords):
            found_test_rows.append(row)
    
    for row in found_test_rows[:3]:  # 只显示前3个匹配的行
        tr_index = all_rows.index(row) + 1
        structured_text = chunker._extract_row_structured_text(header_columns, row, data_rows)
        row_preview = row.text_content().strip()[:30]
        print(f"    tr[{tr_index}]: '{row_preview}...' -> {structured_text}")
